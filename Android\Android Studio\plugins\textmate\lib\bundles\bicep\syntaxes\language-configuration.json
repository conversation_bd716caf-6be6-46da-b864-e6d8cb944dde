{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["'", "'"], ["'''", "'''"]], "autoClosingPairs": [{"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}, {"open": "'''", "close": "'''", "notIn": ["string", "comment"]}, {"open": "/*", "close": "*/", "notIn": ["string", "comment"]}], "autoCloseBefore": ":.,=}])' \n\t", "indentationRules": {"increaseIndentPattern": "^((?!\\/\\/).)*(\\{[^}\"'`]*|\\([^)\"'`]*|\\[[^\\]\"'`]*)$", "decreaseIndentPattern": "^((?!.*?\\/\\*).*\\*/)?\\s*[\\}\\]].*$"}}