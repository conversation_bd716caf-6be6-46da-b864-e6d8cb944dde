{
  "comments": {
    "lineComment": "%"
  },
  "brackets": [
    [ "(", ")" ],
    [ "[", "]" ],
    [ "{", "}" ]
  ],
  "autoClosingPairs": [
    [ "(", ")" ],
    [ "[", "]" ],
    [ "{", "}" ],
    { "open": "'", "close": "'", "notIn": [ "string", "comment" ] },
    { "open": "\"", "close": "\"", "notIn": [ "string" ] },
    { "open": "<<\"", "close": "\">>", "notIn": [ "string" ] }
  ],
  "surroundingPairs": [
    [ "(", ")" ],
    [ "[", "]" ],
    [ "{", "}" ],
    [ "'", "'" ],
    [ "\"", "\"" ]
  ],
  "indentationRules": {
    // Indent if a line ends brackets, "->" or most keywords. Also if prefixed
    // with "||". This should work with most formatting models.
    // The ((?!%).)* is to ensure this doesn't match inside comments.
    "increaseIndentPattern": "^((?!%).)*([{(\\[]|->|after|begin|case|catch|fun|if|of|try|when|maybe|else|(\\|\\|.*))\\s*$",
    // Dedent after brackets, end or lone "->". The latter happens in a spec
    // with indented types, typically after "when". Only do this if it's _only_
    // preceded by whitespace.
    "decreaseIndentPattern": "^\\s*([)}\\]]|end|else|->\\s*$)",
    // Indent if after an incomplete map association operator, list
    // comprehension and type specifier. But only once, then return to the
    // previous indent.
    "indentNextLinePattern": "^((?!%).)*(::|=>|:=|<-)\\s*$"
  },
  "onEnterRules": [
    {
      // Dedent after ";" or "."
      "beforeText": "^((?!%).)*[;.]",
      "action": {
        "indent": "outdent"
      }
    },
    // Inside a comment, pressing enter should insert comment markers (%) as
    // appropriate. These three rules do just that.
    {
      "beforeText": "^\\s*%%%",
      "action": {
        "indent": "none",
        "appendText": "%%% "
      }
    },
    {
      "beforeText": "^\\s*%%",
      "action": {
        "indent": "none",
        "appendText": "%% "
      }
    },
    {
      "beforeText": "^\\s*%",
      "action": {
        "indent": "none",
        "appendText": "% "
      }
    }
  ]
}
