{"name": "twig-language-2", "version": "0.9.4", "description": "Snippets, Syntax Highlighting, Hover, and Formatting for Twig", "license": "MIT", "contributes": {"languages": [{"id": "twig", "aliases": ["HTML (Twig)", "twig"], "extensions": [".twig", ".html.twig"], "configuration": "./src/languages/twig.configuration.json"}], "grammars": [{"language": "twig", "scopeName": "text.html.twig", "path": "./src/syntaxes/twig.tmLanguage", "embeddedLanguages": {"source.json": "json", "source.css": "css", "source.css.scss": "scss", "source.js": "javascript", "source.ts": "typescript"}}], "snippets": [{"language": "twig", "path": "./src/snippets/snippets.json"}], "configuration": {"type": "object", "title": "Twig Language 2", "properties": {"twig-language-2.hover": {"type": "boolean", "default": true, "description": "Whether to enable/disable Twig hover."}, "twig-language-2.formatting": {"type": "boolean", "default": true, "description": "Whether to enable/disable Twig PrettyDiff formatting."}, "twig-language-2.braceLine": {"type": "boolean", "default": false, "description": "If true an empty line will be inserted after opening curly braces and before closing curly braces."}, "twig-language-2.bracePadding": {"type": "boolean", "default": false, "description": "Inserts a space after the start of a container and before the end of the container if the contents of that container are not indented; such as: conditions, function arguments, and escaped sequences of template strings."}, "twig-language-2.braceStyle": {"type": "string", "enum": ["collapse", "collapse-preserve-inline", "expand", "none"], "default": "none", "description": "Emulates JSBeautify's brace_style option using existing Pretty Diff options."}, "twig-language-2.braces": {"type": "boolean", "default": false, "description": "Determines if opening curly braces will exist on the same line as their condition or be forced onto a new line."}, "twig-language-2.commentLine": {"type": "boolean", "default": false, "description": "If a blank new line should be forced above comments."}, "twig-language-2.comments": {"type": "boolean", "default": false, "description": "This will determine whether comments should always start at position 0 of each line or if comments should be indented according to the code."}, "twig-language-2.compressedCss": {"type": "boolean", "default": false, "description": "If CSS should be beautified in a style where the properties and values are minifed for faster reading of selectors."}, "twig-language-2.correct": {"type": "boolean", "default": false, "description": "Automatically correct some sloppiness in code."}, "twig-language-2.cssInsertLines": {"type": "boolean", "default": false, "description": "Inserts new line characters between every CSS code block."}, "twig-language-2.elseLine": {"type": "boolean", "default": false, "description": "If else_line is true then the keyword 'else' is forced onto a new line."}, "twig-language-2.endComma": {"type": "string", "enum": ["always", "never", "none"], "default": false, "description": "If there should be a trailing comma in arrays and objects. Value multiline only applies to modes beautify and diff."}, "twig-language-2.forceAttribute": {"type": "boolean", "default": false, "description": "If all markup attributes should be indented each onto their own line."}, "twig-language-2.forceIndent": {"type": "boolean", "default": false, "description": "Will force indentation upon all content and tags without regard for the creation of new text nodes."}, "twig-language-2.formatArray": {"type": "string", "enum": ["default", "indent", "inline"], "default": "default", "description": "Determines if all array indexes should be indented, never indented, or left to the default."}, "twig-language-2.formatObject": {"type": "string", "enum": ["default", "indent", "inline"], "default": "default", "description": "Determines if all object keys should be indented, never indented, or left to the default."}, "twig-language-2.functionName": {"type": "boolean", "default": false, "description": "If a space should follow a JavaScript function name."}, "twig-language-2.indentStyle": {"type": "string", "enum": ["space", "tab"], "default": "tab", "description": "Choose to indent using tabs or spaces. This formatter will not use the \"Editor: Insert Spaces\" setting, nor will it use the `indent_style` setting declared in `.editorconfig` files."}, "twig-language-2.indentLevel": {"type": "integer", "default": 0, "description": "How much indentation padding should be applied to beautification? This option is internally used for code that requires switching between libraries."}, "twig-language-2.tabSize": {"type": "integer", "default": 0, "description": "0 will default to the editor's tab size (`editor.tabSize`). This formatter will not use the `indent_size` setting declared in `.editorconfig` files."}, "twig-language-2.methodChain": {"type": "integer", "default": 0, "description": "When to break consecutively chained methods and properties onto separate lines. A negative value disables this option. A value of 0 ensures method chains are never broken."}, "twig-language-2.neverFlatten": {"type": "boolean", "default": false, "description": "If destructured lists in script should never be flattend."}, "twig-language-2.newLine": {"type": "boolean", "default": true, "description": "Insert an empty line at the end of output."}, "twig-language-2.noCaseIndent": {"type": "boolean", "default": false, "description": "If a case statement should receive the same indentation as the containing switch block."}, "twig-language-2.noLeadZero": {"type": "boolean", "default": false, "description": "Whether leading 0s in CSS values immediately preceeding a decimal should be removed or prevented."}, "twig-language-2.objectSort": {"type": "boolean", "default": false, "description": "Sorts markup attributes and properties by key name in script and style."}, "twig-language-2.preserve": {"type": "integer", "default": 2, "description": "The maximum number of consecutive empty lines to retain."}, "twig-language-2.preserveComment": {"type": "boolean", "default": false, "description": "Prevent comment reformatting due to option wrap."}, "twig-language-2.quoteConvert": {"type": "string", "enum": ["double", "none", "single"], "default": "none", "description": "If the quotes of script strings or markup attributes should be converted to single quotes or double quotes."}, "twig-language-2.space": {"type": "boolean", "default": true, "description": "Inserts a space following the function keyword for anonymous functions."}, "twig-language-2.spaceClose": {"type": "boolean", "default": false, "description": "Markup self-closing tags end will end with ' />' instead of '/>'."}, "twig-language-2.tagMerge": {"type": "boolean", "default": false, "description": "Allows immediately adjacement start and end markup tags of the same name to be combined into a single self-closing tag."}, "twig-language-2.tagSort": {"type": "boolean", "default": false, "description": "Sort child items of each respective markup parent element."}, "twig-language-2.ternaryLine": {"type": "boolean", "default": true, "description": "If ternary operators in JavaScript ? and : should remain on the same line."}, "twig-language-2.unformatted": {"type": "boolean", "default": false, "description": "If markup tags should have their insides preserved. This option is only available to markup and does not support child tokens that require a different lexer."}, "twig-language-2.variableList": {"type": "string", "enum": ["each", "list", "none"], "default": "none", "description": "If consecutive JavaScript variables should be merged into a comma separated list or if variables in a list should be separated."}, "twig-language-2.vertical": {"type": "boolean", "default": false, "description": "If lists of assignments and properties should be vertically aligned. This option is not used with the markup lexer."}, "twig-language-2.wrap": {"type": "integer", "default": 0, "description": "Character width limit before applying word wrap. A 0 value disables this option. A negative value concatenates script strings."}}}}}