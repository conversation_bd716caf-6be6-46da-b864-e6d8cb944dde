<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>kt</string>
		<string>kts</string>
	</array>
	<key>name</key>
	<string>Kotlin</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#comments</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.other.kotlin</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.package.kotlin</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*(package)\b(?:\s*([^ ;$]+)\s*)?</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#imports</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#statements</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>classes</key>
		<dict>
			<key>begin</key>
			<string>(?=\s*(?:companion|class|object|interface))</string>
			<key>end</key>
			<string>}|(?=$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b(companion\s*)?(class|object|interface)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=&lt;|{|\(|:)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\b(object)\b</string>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\w+</string>
							<key>name</key>
							<string>entity.name.type.class.kotlin</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>&lt;</string>
					<key>end</key>
					<string>&gt;</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#generics</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#parameters</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?={|$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\w+</string>
							<key>name</key>
							<string>entity.other.inherited-class.kotlin</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>\(</string>
							<key>end</key>
							<string>\)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#expressions</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\{</string>
					<key>end</key>
					<string>\}</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#statements</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>comments</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>/\*</string>
					<key>captures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\*/</string>
					<key>name</key>
					<string>comment.block.kotlin</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>comment.line.double-slash.kotlin</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.kotlin</string>
						</dict>
					</dict>
					<key>match</key>
					<string>\s*((//).*$\n?)</string>
				</dict>
			</array>
		</dict>
		<key>constants</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(true|false|null|this|super)\b</string>
					<key>name</key>
					<string>constant.language.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\.?[0-9]*)|(\.[0-9]+))((e|E)(\+|-)?[0-9]+)?)([LlFfUuDd]|UL|ul)?\b</string>
					<key>name</key>
					<string>constant.numeric.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b([A-Z][A-Z0-9_]+)\b</string>
					<key>name</key>
					<string>constant.other.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>expressions</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#expressions</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#types</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#strings</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#constants</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#comments</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#keywords</string>
				</dict>
			</array>
		</dict>
		<key>functions</key>
		<dict>
			<key>begin</key>
			<string>(?=\s*(?:fun))</string>
			<key>end</key>
			<string>}|(?=$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b(fun)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\()</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>&lt;</string>
							<key>end</key>
							<string>&gt;</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#generics</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>entity.name.function.kotlin</string>
								</dict>
							</dict>
							<key>match</key>
							<string>([\.&lt;\?&gt;\w]+\.)?(\w+)</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#parameters</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?={|=|$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\{</string>
					<key>end</key>
					<string>(?=\})</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#statements</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(=)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.assignment.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#expressions</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>generics</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=,|&gt;)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#keywords</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\w+</string>
					<key>name</key>
					<string>storage.type.generic.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>getters-and-setters</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b(get)\b\s*\(\s*\)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}|(?=\bset\b)|$</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(=)</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>keyword.operator.assignment.kotlin</string>
								</dict>
							</dict>
							<key>end</key>
							<string>(?=$|\bset\b)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#expressions</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\{</string>
							<key>end</key>
							<string>\}</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#expressions</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(set)\b\s*(?=\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\}|(?=\bget\b)|$</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\(</string>
							<key>end</key>
							<string>\)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#parameters</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>(=)</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>keyword.operator.assignment.kotlin</string>
								</dict>
							</dict>
							<key>end</key>
							<string>(?=$|\bset\b)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#expressions</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>\{</string>
							<key>end</key>
							<string>\}</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#expressions</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>imports</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*(import)\s+[^ $]+\s+(as)?</string>
				</dict>
			</array>
		</dict>
		<key>keywords</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(var|val|public|private|protected|abstract|final|enum|open|attribute|annotation|override|inline|var|val|vararg|lazy|in|out|internal|data|tailrec|operator|infix|const|yield|typealias|typeof)\b</string>
					<key>name</key>
					<string>storage.modifier.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(try|catch|finally|throw)\b</string>
					<key>name</key>
					<string>keyword.control.catch-exception.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(if|else|while|for|do|return|when|where|break|continue)\b</string>
					<key>name</key>
					<string>keyword.control.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(in|is|as|assert)\b</string>
					<key>name</key>
					<string>keyword.operator.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(==|!=|===|!==|&lt;=|&gt;=|&lt;|&gt;)</string>
					<key>name</key>
					<string>keyword.operator.comparison.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(=)</string>
					<key>name</key>
					<string>keyword.operator.assignment.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(:)</string>
					<key>name</key>
					<string>keyword.operator.declaration.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\.)</string>
					<key>name</key>
					<string>keyword.operator.dot.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\-\-|\+\+)</string>
					<key>name</key>
					<string>keyword.operator.increment-decrement.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\-|\+|\*|\/|%)</string>
					<key>name</key>
					<string>keyword.operator.arithmetic.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\+=|\-=|\*=|\/=)</string>
					<key>name</key>
					<string>keyword.operator.arithmetic.assign.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(!|&amp;&amp;|\|\|)</string>
					<key>name</key>
					<string>keyword.operator.logical.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\.\.)</string>
					<key>name</key>
					<string>keyword.operator.range.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(;)</string>
					<key>name</key>
					<string>punctuation.terminator.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>namespaces</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(namespace)\b</string>
					<key>name</key>
					<string>keyword.other.kotlin</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\{</string>
					<key>end</key>
					<string>\}</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#statements</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>parameters</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=,|\)|=)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(=)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=,|\))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#expressions</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#keywords</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\w+</string>
					<key>name</key>
					<string>variable.parameter.function.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>statements</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#namespaces</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#typedefs</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#classes</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#functions</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variables</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#getters-and-setters</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#expressions</string>
				</dict>
			</array>
		</dict>
		<key>strings</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>"""</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>"""</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.kotlin</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.third.kotlin</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(\$\w+|\$\{[^\}]+\})</string>
							<key>name</key>
							<string>variable.parameter.template.kotlin</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\\.</string>
							<key>name</key>
							<string>constant.character.escape.kotlin</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>"</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>"</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.kotlin</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.double.kotlin</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>(\$\w+|\$\{[^\}]+\})</string>
							<key>name</key>
							<string>variable.parameter.template.kotlin</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\\.</string>
							<key>name</key>
							<string>constant.character.escape.kotlin</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>'</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.kotlin</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.single.kotlin</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>\\.</string>
							<key>name</key>
							<string>constant.character.escape.kotlin</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>`</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>`</string>
					<key>endCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.kotlin</string>
						</dict>
					</dict>
					<key>name</key>
					<string>string.quoted.single.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>typedefs</key>
		<dict>
			<key>begin</key>
			<string>(?=\s*(?:type))</string>
			<key>end</key>
			<string>(?=$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(type)\b</string>
					<key>name</key>
					<string>keyword.other.kotlin</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>&lt;</string>
					<key>end</key>
					<string>&gt;</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#generics</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#expressions</string>
				</dict>
			</array>
		</dict>
		<key>types</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\b(Any|Unit|String|Int|Boolean|Char|Long|Double|Float|Short|Byte|dynamic)\b</string>
					<key>name</key>
					<string>storage.type.buildin.kotlin</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\b(IntArray|BooleanArray|CharArray|LongArray|DoubleArray|FloatArray|ShortArray|ByteArray)\b</string>
					<key>name</key>
					<string>storage.type.buildin.array.kotlin</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(Array|List|Map)&lt;\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.type.buildin.collection.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>&gt;</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#keywords</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\w+&lt;</string>
					<key>end</key>
					<string>&gt;</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#keywords</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(#)\(</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.tuple.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#expressions</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\{</string>
					<key>end</key>
					<string>\}</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#statements</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>end</key>
					<string>\)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>(-&gt;)</string>
					<key>name</key>
					<string>keyword.operator.declaration.kotlin</string>
				</dict>
			</array>
		</dict>
		<key>variables</key>
		<dict>
			<key>begin</key>
			<string>(?=\s*(?:var|val))</string>
			<key>end</key>
			<string>(?=:|=|$)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b(var|val)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=:|=|$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>&lt;</string>
							<key>end</key>
							<string>&gt;</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#generics</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>captures</key>
							<dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>entity.name.variable.kotlin</string>
								</dict>
							</dict>
							<key>match</key>
							<string>([\.&lt;\?&gt;\w]+\.)?(\w+)</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.declaration.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?==|$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#types</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#getters-and-setters</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(=)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.assignment.kotlin</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=$)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#expressions</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#getters-and-setters</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scope</key>
	<string>source.Kotlin</string>
	<key>scopeName</key>
	<string>source.Kotlin</string>
	<key>uuid</key>
	<string>d508c059-a938-4779-b2bc-ff43a5078907</string>
</dict>
</plist>
