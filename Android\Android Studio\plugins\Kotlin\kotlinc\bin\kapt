#!/usr/bin/env bash

# Copyright 2010-2021 JetBrains s.r.o. and Kotlin Programming Language contributors.
# Use of this source code is governed by the Apache 2.0 license that can be found in the license/LICENSE.txt file.

export KOTLIN_TOOL=kotlin-annotation-processing-cli.jar

export KOTLIN_COMPILER=org.jetbrains.kotlin.kapt.cli.KaptCli

DIR="${BASH_SOURCE[0]%/*}"
: ${DIR:="."}

"${DIR}"/kotlinc "$@"
