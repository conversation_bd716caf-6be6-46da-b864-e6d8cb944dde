{"comments": {"lineComment": "#", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [{"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}], "autoCloseBefore": ";:.,=}])> \n\t\"", "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"]], "folding": {"markers": {"start": "^\\s*#region", "end": "^\\s*#endregion"}}, "indentationRules": {"increaseIndentPattern": "^((?!\\/\\/).)*(\\{[^}\"'`]*|\\([^)\"'`]*|\\[[^\\]\"'`]*)$", "decreaseIndentPattern": "^((?!.*?\\/\\*).*\\*/)?\\s*[\\)\\}\\]].*$"}}