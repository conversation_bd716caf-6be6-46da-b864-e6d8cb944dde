<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>CMakeCache.txt</string>
	</array>
	<key>name</key>
	<string>CMake</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>match</key>
			<string>//+.*$|#+.*$</string>
			<key>name</key>
			<string>comment.source.cmakecache</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Cache Entries</string>
			<key>match</key>
			<string>\b-ADVANCED\b</string>
			<key>name</key>
			<string>entity.source.cmakecache</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Literal values</string>
			<key>match</key>
			<string>\b(?i:(YES|NO|ON|OFF|TRUE|FALSE|Y|N|\d+))$</string>
			<key>name</key>
			<string>constant.source.cmakecache</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Normal Strings</string>
			<key>begin</key>
			<string>=</string>
			<key>end</key>
			<string>$</string>
			<key>name</key>
			<string>string.source.cmakecache</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Cache Entry Names</string>
			<key>match</key>
			<string>^\b(?i:(\w+))\b</string>
			<key>name</key>
			<string>variable.source.cmakecache</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Cache Entry Types</string>
			<key>begin</key>
			<string>:</string>
			<key>end</key>
			<string>=</string>
			<key>match</key>
			<string>(BOOL|STRING|FILEPATH|PATH|STATIC|INTERNAL)</string>
			<key>name</key>
			<string>keyword.source.cmakecache</string>
		</dict>
	</array>
	<key>repository</key>


	<dict>

	</dict>
	<key>scopeName</key>
	<string>source.cmakecache</string>
	<key>uuid</key>
	<string>b545774b-6d11-4f08-bb0a-e64cccc9528c</string>
</dict>
</plist>