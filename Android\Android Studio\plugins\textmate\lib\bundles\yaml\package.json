{"name": "yaml", "version": "1.0.0", "description": "%description%", "license": "MIT", "contributes": {"languages": [{"id": "dockercompose", "aliases": ["Compose", "compose"], "filenamePatterns": ["compose.yml", "compose.yaml", "compose.*.yml", "compose.*.yaml", "*docker*compose*.yml", "*docker*compose*.yaml"], "configuration": "./language-configuration.json"}, {"id": "yaml", "aliases": ["YAML", "yaml"], "extensions": [".yaml", ".yml", ".e<PERSON>l", ".eyml", ".cff", ".yaml-tmlanguage", ".yaml-tmpreferences", ".yaml-tmtheme"], "firstLine": "^#cloud-config", "configuration": "./language-configuration.json"}], "grammars": [{"language": "dockercompose", "scopeName": "source.yaml", "path": "./syntaxes/yaml.tmLanguage.json"}, {"language": "yaml", "scopeName": "source.yaml", "path": "./syntaxes/yaml.tmLanguage.json"}], "configurationDefaults": {"[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "advanced", "diffEditor.ignoreTrimWhitespace": false}, "[dockercompose]": {"editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "advanced"}}}}