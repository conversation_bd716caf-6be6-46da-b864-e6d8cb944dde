{
	"comments": {
		"lineComment": "",
		"blockComment": ["<!--", "-->"]
	},
	"brackets": [
		["<!--", "-->"],
		["<", ">"],
		["{", "}"],
		["(", ")"],
		["[", "]"]
	],
	"wordPattern": {
		"pattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)"
	}

	// enhancedBrackets: [{
	// 	tokenType: 'tag.tag-$1.xml',
	// 	openTrigger: '>',
	// 	open: /<(\w[\w\d]*)([^\/>]*(?!\/)>)[^<>]*$/i,
	// 	closeComplete: '</$1>',
	// 	closeTrigger: '>',
	// 	close: /<\/(\w[\w\d]*)\s*>$/i
	// }],

	// autoClosingPairs:  [['\'', '\''], ['"', '"'] ]
}
