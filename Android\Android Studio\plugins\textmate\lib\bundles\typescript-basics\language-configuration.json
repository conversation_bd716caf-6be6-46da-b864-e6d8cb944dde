{
	// Note that this file should stay in sync with 'javascript-language-basics/javascript-language-configuration.json'
	"comments": {
		"lineComment": "//",
		"blockComment": [
			"/*",
			"*/"
		]
	},
	"brackets": [
		[
			"${",
			"}"
		],
		[
			"{",
			"}"
		],
		[
			"[",
			"]"
		],
		[
			"(",
			")"
		]
	],
	"autoClosingPairs": [
		{
			"open": "{",
			"close": "}"
		},
		{
			"open": "[",
			"close": "]"
		},
		{
			"open": "(",
			"close": ")"
		},
		{
			"open": "'",
			"close": "'",
			"notIn": [
				"string",
				"comment"
			]
		},
		{
			"open": "\"",
			"close": "\"",
			"notIn": [
				"string"
			]
		},
		{
			"open": "`",
			"close": "`",
			"notIn": [
				"string",
				"comment"
			]
		},
		{
			"open": "/**",
			"close": " */",
			"notIn": [
				"string"
			]
		}
	],
	"surroundingPairs": [
		[
			"{",
			"}"
		],
		[
			"[",
			"]"
		],
		[
			"(",
			")"
		],
		[
			"'",
			"'"
		],
		[
			"\"",
			"\""
		],
		[
			"`",
			"`"
		],
		[
			"<",
			">"
		]
	],
	"colorizedBracketPairs": [
		[
			"(",
			")"
		],
		[
			"[",
			"]"
		],
		[
			"{",
			"}"
		],
		[
			"<",
			">"
		]
	],
	"autoCloseBefore": ";:.,=}])>` \n\t",
	"folding": {
		"markers": {
			"start": "^\\s*//\\s*#?region\\b",
			"end": "^\\s*//\\s*#?endregion\\b"
		}
	},
	"wordPattern": {
		"pattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\@\\~\\!\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>/\\?\\s]+)",
	},
	"indentationRules": {
		"decreaseIndentPattern": {
			"pattern": "^\\s*[\\}\\]\\)].*$"
		},
		"increaseIndentPattern": {
			"pattern": "^.*(\\{[^}]*|\\([^)]*|\\[[^\\]]*)$"
		},
		// e.g.  * ...| or */| or *-----*/|
		"unIndentedLinePattern": {
			"pattern": "^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$|^(\\t|[ ])*[ ]\\*/\\s*$|^(\\t|[ ])*\\*([ ]([^\\*]|\\*(?!/))*)?$"
		},
		"indentNextLinePattern": {
			"pattern": "^((.*=>\\s*)|((.*[^\\w]+|\\s*)(if|while|for)\\s*\\(.*\\)\\s*))$"
		}
	},
	"onEnterRules": [
		{
			// e.g. /** | */
			"beforeText": {
				"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"
			},
			"afterText": {
				"pattern": "^\\s*\\*/$"
			},
			"action": {
				"indent": "indentOutdent",
				"appendText": " * "
			}
		},
		{
			// e.g. /** ...|
			"beforeText": {
				"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"
			},
			"action": {
				"indent": "none",
				"appendText": " * "
			}
		},
		{
			// e.g.  * ...|
			"beforeText": {
				"pattern": "^(\\t|[ ])*\\*([ ]([^\\*]|\\*(?!/))*)?$"
			},
			"previousLineText": {
				"pattern": "(?=^(\\s*(/\\*\\*|\\*)).*)(?=(?!(\\s*\\*/)))"
			},
			"action": {
				"indent": "none",
				"appendText": "* "
			}
		},
		{
			// e.g.  */|
			"beforeText": {
				"pattern": "^(\\t|[ ])*[ ]\\*/\\s*$"
			},
			"action": {
				"indent": "none",
				"removeText": 1
			},
		},
		{
			// e.g.  *-----*/|
			"beforeText": {
				"pattern": "^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$"
			},
			"action": {
				"indent": "none",
				"removeText": 1
			},
		},
		{
			"beforeText": {
				"pattern": "^\\s*(\\bcase\\s.+:|\\bdefault:)$"
			},
			"afterText": {
				"pattern": "^(?!\\s*(\\bcase\\b|\\bdefault\\b))"
			},
			"action": {
				"indent": "indent"
			}
		},
		{
			// Decrease indentation after single line if/else if/else, for, or while
			"previousLineText": "^\\s*(((else ?)?if|for|while)\\s*\\(.*\\)\\s*|else\\s*)$",
			// But make sure line doesn't have braces or is not another if statement
			"beforeText": "^\\s+([^{i\\s]|i(?!f\\b))",
			"action": {
				"indent": "outdent"
			}
		},
		// Indent when pressing enter from inside ()
		{
			"beforeText": "^.*\\([^\\)]*$",
			"afterText": "^\\s*\\).*$",
			"action": {
				"indent": "indentOutdent",
				"appendText": "\t",
			}
		},
		// Indent when pressing enter from inside {}
		{
			"beforeText": "^.*\\{[^\\}]*$",
			"afterText": "^\\s*\\}.*$",
			"action": {
				"indent": "indentOutdent",
				"appendText": "\t",
			}
		},
		// Indent when pressing enter from inside []
		{
			"beforeText": "^.*\\[[^\\]]*$",
			"afterText": "^\\s*\\].*$",
			"action": {
				"indent": "indentOutdent",
				"appendText": "\t",
			}
		},
	]
}
