<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>$schema</key>
    <string>https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json</string>
    <key>name</key>
    <string>Bicep</string>
    <key>scopeName</key>
    <string>source.bicep</string>
    <key>fileTypes</key>
    <array>
      <string>.bicep</string>
    </array>
    <key>patterns</key>
    <array>
      <dict>
        <key>include</key>
        <string>#expression</string>
      </dict>
      <dict>
        <key>include</key>
        <string>#comments</string>
      </dict>
    </array>
    <key>repository</key>
    <dict>
      <key>array-literal</key>
      <dict>
        <key>name</key>
        <string>meta.array-literal.bicep</string>
        <key>begin</key>
        <string>\[(?!(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\bfor\b)</string>
        <key>end</key>
        <string>]</string>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#expression</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>block-comment</key>
      <dict>
        <key>name</key>
        <string>comment.block.bicep</string>
        <key>begin</key>
        <string>/\*</string>
        <key>end</key>
        <string>\*/</string>
      </dict>
      <key>comments</key>
      <dict>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#line-comment</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#block-comment</string>
          </dict>
        </array>
      </dict>
      <key>decorator</key>
      <dict>
        <key>name</key>
        <string>meta.decorator.bicep</string>
        <key>begin</key>
        <string>@(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*(?=\b[_$[:alpha:]][_$[:alnum:]]*\b)</string>
        <key>end</key>
        <string/>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#expression</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>directive</key>
      <dict>
        <key>name</key>
        <string>meta.directive.bicep</string>
        <key>begin</key>
        <string>#\b[_a-zA-Z-0-9]+\b</string>
        <key>end</key>
        <string>$</string>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#directive-variable</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>directive-variable</key>
      <dict>
        <key>name</key>
        <string>keyword.control.declaration.bicep</string>
        <key>match</key>
        <string>\b[_a-zA-Z-0-9]+\b</string>
      </dict>
      <key>escape-character</key>
      <dict>
        <key>name</key>
        <string>constant.character.escape.bicep</string>
        <key>match</key>
        <string>\\(u{[0-9A-Fa-f]+}|n|r|t|\\|'|\${)</string>
      </dict>
      <key>expression</key>
      <dict>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#string-literal</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#string-verbatim</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#numeric-literal</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#named-literal</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#object-literal</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#array-literal</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#keyword</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#identifier</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#function-call</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#decorator</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#lambda-start</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#directive</string>
          </dict>
        </array>
      </dict>
      <key>function-call</key>
      <dict>
        <key>name</key>
        <string>meta.function-call.bicep</string>
        <key>begin</key>
        <string>(\b[_$[:alpha:]][_$[:alnum:]]*\b)(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\(</string>
        <key>beginCaptures</key>
        <dict>
          <key>1</key>
          <dict>
            <key>name</key>
            <string>entity.name.function.bicep</string>
          </dict>
        </dict>
        <key>end</key>
        <string>\)</string>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#expression</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>identifier</key>
      <dict>
        <key>name</key>
        <string>variable.other.readwrite.bicep</string>
        <key>match</key>
        <string>\b[_$[:alpha:]][_$[:alnum:]]*\b(?!(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\()</string>
      </dict>
      <key>keyword</key>
      <dict>
        <key>name</key>
        <string>keyword.control.declaration.bicep</string>
        <key>match</key>
        <string>\b(metadata|targetScope|resource|module|param|var|output|for|in|if|existing|import|as|type|with)\b</string>
      </dict>
      <key>lambda-start</key>
      <dict>
        <key>name</key>
        <string>meta.lambda-start.bicep</string>
        <key>begin</key>
        <string>(\((?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\b[_$[:alpha:]][_$[:alnum:]]*\b(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*(,(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\b[_$[:alpha:]][_$[:alnum:]]*\b(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*)*\)|\((?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\)|(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*\b[_$[:alpha:]][_$[:alnum:]]*\b(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*)(?=(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*=&gt;)</string>
        <key>beginCaptures</key>
        <dict>
          <key>1</key>
          <dict>
            <key>name</key>
            <string>meta.undefined.bicep</string>
            <key>patterns</key>
            <array>
              <dict>
                <key>include</key>
                <string>#identifier</string>
              </dict>
              <dict>
                <key>include</key>
                <string>#comments</string>
              </dict>
            </array>
          </dict>
        </dict>
        <key>end</key>
        <string>(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*=&gt;</string>
      </dict>
      <key>line-comment</key>
      <dict>
        <key>name</key>
        <string>comment.line.double-slash.bicep</string>
        <key>match</key>
        <string>//.*(?=$)</string>
      </dict>
      <key>named-literal</key>
      <dict>
        <key>name</key>
        <string>constant.language.bicep</string>
        <key>match</key>
        <string>\b(true|false|null)\b</string>
      </dict>
      <key>numeric-literal</key>
      <dict>
        <key>name</key>
        <string>constant.numeric.bicep</string>
        <key>match</key>
        <string>[0-9]+</string>
      </dict>
      <key>object-literal</key>
      <dict>
        <key>name</key>
        <string>meta.object-literal.bicep</string>
        <key>begin</key>
        <string>{</string>
        <key>end</key>
        <string>}</string>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#object-property-key</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#expression</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>object-property-key</key>
      <dict>
        <key>name</key>
        <string>variable.other.property.bicep</string>
        <key>match</key>
        <string>\b[_$[:alpha:]][_$[:alnum:]]*\b(?=(?:[ \t\r\n]|\/\*(?:\*(?!\/)|[^*])*\*\/)*:)</string>
      </dict>
      <key>string-literal</key>
      <dict>
        <key>name</key>
        <string>string.quoted.single.bicep</string>
        <key>begin</key>
        <string>'(?!'')</string>
        <key>end</key>
        <string>'</string>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#escape-character</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#string-literal-subst</string>
          </dict>
        </array>
      </dict>
      <key>string-literal-subst</key>
      <dict>
        <key>name</key>
        <string>meta.string-literal-subst.bicep</string>
        <key>begin</key>
        <string>(?&lt;!\\)(\${)</string>
        <key>beginCaptures</key>
        <dict>
          <key>1</key>
          <dict>
            <key>name</key>
            <string>punctuation.definition.template-expression.begin.bicep</string>
          </dict>
        </dict>
        <key>end</key>
        <string>(})</string>
        <key>endCaptures</key>
        <dict>
          <key>1</key>
          <dict>
            <key>name</key>
            <string>punctuation.definition.template-expression.end.bicep</string>
          </dict>
        </dict>
        <key>patterns</key>
        <array>
          <dict>
            <key>include</key>
            <string>#expression</string>
          </dict>
          <dict>
            <key>include</key>
            <string>#comments</string>
          </dict>
        </array>
      </dict>
      <key>string-verbatim</key>
      <dict>
        <key>name</key>
        <string>string.quoted.multi.bicep</string>
        <key>begin</key>
        <string>'''</string>
        <key>end</key>
        <string>'''</string>
        <key>patterns</key>
        <array/>
      </dict>
    </dict>
  </dict>
</plist>