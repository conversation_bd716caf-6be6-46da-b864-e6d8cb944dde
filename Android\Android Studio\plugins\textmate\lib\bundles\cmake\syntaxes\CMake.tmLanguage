<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>cmake</string>
		<string>CMakeLists.txt</string>
	</array>
	<key>name</key>
	<string>CMake</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>comment</key>
			<string>Variables That Describe the System</string>
			<key>match</key>
			<string>\b(?i:APPLE|BORLAND|(CMAKE_)?(CL_64|COMPILER_2005|HOST_APPLE|HOST_SYSTEM|HOST_SYSTEM_NAME|HOST_SYSTEM_PROCESSOR|HOST_SYSTEM_VERSION|HOST_UNIX|HOST_WIN32|LIBRARY_ARCHITECTURE|LIBRARY_ARCHITECTURE_REGEX|OBJECT_PATH_MAX|SYSTEM|SYSTEM_NAME|SYSTEM_PROCESSOR|SYSTEM_VERSION)|CYGWIN|MSVC|MSVC80|MSVC_IDE|MSVC_VERSION|UNIX|WIN32|XCODE_VERSION|MSVC60|MSVC70|MSVC90|MSVC71)\b</string>
			<key>name</key>
			<string>constant.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>cmakeOperators</string>
			<key>match</key>
			<string>\b(?i:ABSOLUTE|AND|BOOL|CACHE|COMMAND|COMMENT|DEFINED|DOC|EQUAL|EXISTS|EXT|FALSE|GREATER|GREATER_EQUAL|INTERNAL|IN_LIST|IS_ABSOLUTE|IS_DIRECTORY|IS_NEWER_THAN|IS_SYMLINK|LESS|LESS_EQUAL|MATCHES|NAME|NAMES|NAME_WE|NOT|OFF|ON|OR|PATH|PATHS|POLICY|PROGRAM|STREQUAL|STRGREATER|STRGREATER_EQUAL|STRING|STRLESS|STRLESS_EQUAL|TARGET|TEST|TRUE|VERSION_EQUAL|VERSION_GREATER|VERSION_GREATER_EQUAL|VERSION_LESS)\b</string>
			<key>name</key>
			<string>keyword.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Commands</string>
			<key>match</key>
			<string>^\s*\b(?i:add_compile_options|add_custom_command|add_custom_target|add_definitions|add_dependencies|add_executable|add_library|add_subdirectory|add_test|aux_source_directory|break|build_command|build_name|cmake_host_system_information|cmake_minimum_required|cmake_policy|configure_file|continue|create_test_sourcelist|ctest_build|ctest_configure|ctest_coverage|ctest_empty_binary_directory|ctest_memcheck|ctest_read_custom_files|ctest_run_script|ctest_sleep|ctest_start|ctest_submit|ctest_test|ctest_update|ctest_upload|define_property|else|elseif|enable_language|enable_testing|endforeach|endfunction|endif|endmacro|endwhile|exec_program|execute_process|export|export_library_dependencies|file|find_file|find_library|find_package|find_path|find_program|fltk_wrap_ui|foreach|function|get_cmake_property|get_directory_property|get_filename_component|get_property|get_source_file_property|get_target_property|get_test_property|if|include|include_directories|include_external_msproject|include_regular_expression|install|install_files|install_programs|install_targets|link_directories|link_libraries|list|load_cache|load_command|macro|make_directory|mark_as_advanced|math|message|option|output_required_files|project|qt_wrap_cpp|qt_wrap_ui|remove|remove_definitions|return|separate_arguments|set|set_directory_properties|set_property|set_source_files_properties|set_target_properties|set_tests_properties|site_name|source_group|string|subdir_depends|subdirs|target_compile_definitions|target_compile_features|target_compile_options|target_include_directories|target_link_libraries|target_sources|try_compile|try_run|unset|use_mangled_mesa|utility_source|variable_requires|variable_watch|while|write_file)\b</string>
			<key>name</key>
			<string>keyword.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Variables That Change Behavior</string>
			<key>match</key>
			<string>\b(?i:BUILD_SHARED_LIBS|(CMAKE_)?(ABSOLUTE_DESTINATION_FILES|AUTOMOC_RELAXED_MODE|BACKWARDS_COMPATIBILITY|BUILD_TYPE|COLOR_MAKEFILE|CONFIGURATION_TYPES|DEBUG_TARGET_PROPERTIES|DISABLE_FIND_PACKAGE_\w+|FIND_LIBRARY_PREFIXES|FIND_LIBRARY_SUFFIXES|IGNORE_PATH|INCLUDE_PATH|INSTALL_DEFAULT_COMPONENT_NAME|INSTALL_PREFIX|LIBRARY_PATH|MFC_FLAG|MODULE_PATH|NOT_USING_CONFIG_FLAGS|POLICY_DEFAULT_CMP\w+|PREFIX_PATH|PROGRAM_PATH|SKIP_INSTALL_ALL_DEPENDENCY|SYSTEM_IGNORE_PATH|SYSTEM_INCLUDE_PATH|SYSTEM_LIBRARY_PATH|SYSTEM_PREFIX_PATH|SYSTEM_PROGRAM_PATH|USER_MAKE_RULES_OVERRIDE|WARN_ON_ABSOLUTE_INSTALL_DESTINATION))\b</string>
			<key>name</key>
			<string>variable.source.cmake</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\$\{\w+\}</string>
			<key>name</key>
			<string>storage.source.cmake</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\$ENV\{\w+\}</string>
			<key>name</key>
			<string>storage.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Variables that Control the Build</string>
			<key>match</key>
			<string>\b(?i:(CMAKE_)?(\w+_POSTFIX|ARCHIVE_OUTPUT_DIRECTORY|AUTOMOC|AUTOMOC_MOC_OPTIONS|BUILD_WITH_INSTALL_RPATH|DEBUG_POSTFIX|EXE_LINKER_FLAGS|EXE_LINKER_FLAGS_\w+|Fortran_FORMAT|Fortran_MODULE_DIRECTORY|GNUtoMS|INCLUDE_CURRENT_DIR|INCLUDE_CURRENT_DIR_IN_INTERFACE|INSTALL_NAME_DIR|INSTALL_RPATH|INSTALL_RPATH_USE_LINK_PATH|LIBRARY_OUTPUT_DIRECTORY|LIBRARY_PATH_FLAG|LINK_DEF_FILE_FLAG|LINK_DEPENDS_NO_SHARED|LINK_INTERFACE_LIBRARIES|LINK_LIBRARY_FILE_FLAG|LINK_LIBRARY_FLAG|MACOSX_BUNDLE|NO_BUILTIN_CHRPATH|PDB_OUTPUT_DIRECTORY|POSITION_INDEPENDENT_CODE|RUNTIME_OUTPUT_DIRECTORY|SKIP_BUILD_RPATH|SKIP_INSTALL_RPATH|TRY_COMPILE_CONFIGURATION|USE_RELATIVE_PATHS|WIN32_EXECUTABLE)|EXECUTABLE_OUTPUT_PATH|LIBRARY_OUTPUT_PATH)\b</string>
			<key>name</key>
			<string>variable.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Variables that Provide Information</string>
			<key>match</key>
			<string>\b(?i:CMAKE_(AR|ARGC|ARGV0|BINARY_DIR|BUILD_TOOL|CACHEFILE_DIR|CACHE_MAJOR_VERSION|CACHE_MINOR_VERSION|CACHE_PATCH_VERSION|CFG_INTDIR|COMMAND|CROSSCOMPILING|CTEST_COMMAND|CURRENT_BINARY_DIR|CURRENT_LIST_DIR|CURRENT_LIST_FILE|CURRENT_LIST_LINE|CURRENT_SOURCE_DIR|DL_LIBS|EDIT_COMMAND|EXECUTABLE_SUFFIX|EXTRA_GENERATOR|EXTRA_SHARED_LIBRARY_SUFFIXES|GENERATOR|HOME_DIRECTORY|IMPORT_LIBRARY_PREFIX|IMPORT_LIBRARY_SUFFIX|LINK_LIBRARY_SUFFIX|MAJOR_VERSION|MAKE_PROGRAM|MINOR_VERSION|PARENT_LIST_FILE|PATCH_VERSION|PROJECT_NAME|RANLIB|ROOT|SCRIPT_MODE_FILE|SHARED_LIBRARY_PREFIX|SHARED_LIBRARY_SUFFIX|SHARED_MODULE_PREFIX|SHARED_MODULE_SUFFIX|SIZEOF_VOID_P|SKIP_RPATH|SOURCE_DIR|STANDARD_LIBRARIES|STATIC_LIBRARY_PREFIX|STATIC_LIBRARY_SUFFIX|TWEAK_VERSION|USING_VC_FREE_TOOLS|VERBOSE_MAKEFILE|VERSION)|PROJECT_BINARY_DIR|PROJECT_NAME|PROJECT_SOURCE_DIR|\w+_BINARY_DIR|\w+__SOURCE_DIR)\b</string>
			<key>name</key>
			<string>variable.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>BracketArgs</string>
			<key>begin</key>
			<string>#\[(=*)\[</string>
			<key>end</key>
			<string>\]\1\]</string>
			<key>name</key>
			<string>comment.source.cmake</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\(.|$)</string>
					<key>name</key>
					<string>constant.character.escape</string>
				</dict>
			</array>
		</dict>	
		
		<dict>
			<key>comment</key>
			<string>BracketArgs</string>
			<key>begin</key>
			<string>\[(=*)\[</string>
			<key>end</key>
			<string>\]\1\]</string>
			<key>name</key>
			<string>argument.source.cmake</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\(.|$)</string>
					<key>name</key>
					<string>constant.character.escape</string>
				</dict>
			</array>
		</dict>	
			
		<dict>
			<key>match</key>
			<string>#+.*$</string>
			<key>name</key>
			<string>comment.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Cache Entries</string>
			<key>match</key>
			<string>\b(?i:ADVANCED|HELPSTRING|MODIFIED|STRINGS|TYPE|VALUE)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Source Files</string>
			<key>match</key>
			<string>\b(?i:ABSTRACT|COMPILE_DEFINITIONS|COMPILE_DEFINITIONS_&lt;CONFIG&gt;|COMPILE_FLAGS|EXTERNAL_OBJECT|Fortran_FORMAT|GENERATED|HEADER_FILE_ONLY|KEEP_EXTENSION|LABELS|LANGUAGE|LOCATION|MACOSX_PACKAGE_LOCATION|OBJECT_DEPENDS|OBJECT_OUTPUTS|SYMBOLIC|WRAP_EXCLUDE)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Tests</string>
			<key>match</key>
			<string>\b(?i:ATTACHED_FILES|ATTACHED_FILES_ON_FAIL|COST|DEPENDS|ENVIRONMENT|FAIL_REGULAR_EXPRESSION|LABELS|MEASUREMENT|PASS_REGULAR_EXPRESSION|PROCESSORS|REQUIRED_FILES|RESOURCE_LOCK|RUN_SERIAL|TIMEOUT|WILL_FAIL|WORKING_DIRECTORY)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Directories</string>
			<key>match</key>
			<string>\b(?i:ADDITIONAL_MAKE_CLEAN_FILES|CACHE_VARIABLES|CLEAN_NO_CUSTOM|COMPILE_DEFINITIONS|COMPILE_DEFINITIONS_\w+|DEFINITIONS|EXCLUDE_FROM_ALL|IMPLICIT_DEPENDS_INCLUDE_TRANSFORM|INCLUDE_DIRECTORIES|INCLUDE_REGULAR_EXPRESSION|INTERPROCEDURAL_OPTIMIZATION|INTERPROCEDURAL_OPTIMIZATION_\w+|LINK_DIRECTORIES|LISTFILE_STACK|MACROS|PARENT_DIRECTORY|RULE_LAUNCH_COMPILE|RULE_LAUNCH_CUSTOM|RULE_LAUNCH_LINK|TEST_INCLUDE_FILE|VARIABLES|VS_GLOBAL_SECTION_POST_\w+|VS_GLOBAL_SECTION_PRE_\w+)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties of Global Scope</string>
			<key>match</key>
			<string>\b(?i:ALLOW_DUPLICATE_CUSTOM_TARGETS|DEBUG_CONFIGURATIONS|DISABLED_FEATURES|ENABLED_FEATURES|ENABLED_LANGUAGES|FIND_LIBRARY_USE_LIB64_PATHS|FIND_LIBRARY_USE_OPENBSD_VERSIONING|GLOBAL_DEPENDS_DEBUG_MODE|GLOBAL_DEPENDS_NO_CYCLES|IN_TRY_COMPILE|PACKAGES_FOUND|PACKAGES_NOT_FOUND|PREDEFINED_TARGETS_FOLDER|REPORT_UNDEFINED_PROPERTIES|RULE_LAUNCH_COMPILE|RULE_LAUNCH_CUSTOM|RULE_LAUNCH_LINK|RULE_MESSAGES|TARGET_ARCHIVES_MAY_BE_SHARED_LIBS|TARGET_SUPPORTS_SHARED_LIBS|USE_FOLDERS|__CMAKE_DELETE_CACHE_CHANGE_VARS_)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Properties on Targets</string>
			<key>match</key>
			<string>\b(?i:\w+_(OUTPUT_NAME|POSTFIX)|ARCHIVE_OUTPUT_(DIRECTORY(_\w+)?|NAME(_\w+)?)|AUTOMOC(_MOC_OPTIONS)?|BUILD_WITH_INSTALL_RPATH|BUNDLE|BUNDLE(_EXTENSION)?|COMPATIBLE_INTERFACE_BOOL|COMPATIBLE_INTERFACE_STRING|COMPILE_(DEFINITIONS(_\w+)?|FLAGS)|DEBUG_POSTFIX|DEFINE_SYMBOL|ENABLE_EXPORTS|EXCLUDE_FROM_ALL|EchoString|FOLDER|FRAMEWORK|Fortran_(FORMAT|MODULE_DIRECTORY)|GENERATOR_FILE_NAME|GNUtoMS|HAS_CXX|IMPLICIT_DEPENDS_INCLUDE_TRANSFORM|IMPORTED|IMPORTED_(CONFIGURATIONS|IMPLIB(_\w+)?|LINK_DEPENDENT_LIBRARIES(_\w+)?|LINK_INTERFACE_LANGUAGES(_\w+)?|LINK_INTERFACE_LIBRARIES(_\w+)?|LINK_INTERFACE_MULTIPLICITY(_\w+)?|LOCATION(_\w+)?|NO_SONAME(_\w+)?|SONAME(_\w+)?)|IMPORT_PREFIX|IMPORT_SUFFIX|INSTALL_NAME_DIR|INSTALL_RPATH|INSTALL_RPATH_USE_LINK_PATH|INTERFACE|INTERFACE_COMPILE_DEFINITIONS|INTERFACE_INCLUDE_DIRECTORIES|INTERPROCEDURAL_OPTIMIZATION|INTERPROCEDURAL_OPTIMIZATION_\w+|LABELS|LIBRARY_OUTPUT_DIRECTORY(_\w+)?|LIBRARY_OUTPUT_NAME(_\w+)?|LINKER_LANGUAGE|LINK_DEPENDS|LINK_FLAGS(_\w+)?|LINK_INTERFACE_LIBRARIES(_\w+)?|LINK_INTERFACE_MULTIPLICITY(_\w+)?|LINK_LIBRARIES|LINK_SEARCH_END_STATIC|LINK_SEARCH_START_STATIC|LOCATION(_\w+)?|MACOSX_BUNDLE|MACOSX_BUNDLE_INFO_PLIST|MACOSX_FRAMEWORK_INFO_PLIST|MAP_IMPORTED_CONFIG_\w+|NO_SONAME|OSX_ARCHITECTURES(_\w+)?|OUTPUT_NAME(_\w+)?|PDB_NAME(_\w+)?|POST_INSTALL_SCRIPT|PREFIX|PRE_INSTALL_SCRIPT|PRIVATE|PRIVATE_HEADER|PROJECT_LABEL|PUBLIC|PUBLIC_HEADER|RESOURCE|RULE_LAUNCH_(COMPILE|CUSTOM|LINK)|RUNTIME_OUTPUT_(DIRECTORY(_\w+)?|NAME(_\w+)?)|SKIP_BUILD_RPATH|SOURCES|SOVERSION|STATIC_LIBRARY_FLAGS(_\w+)?|SUFFIX|TYPE|VERSION|VS_DOTNET_REFERENCES|VS_GLOBAL_(\w+|KEYWORD|PROJECT_TYPES)|VS_KEYWORD|VS_SCC_(AUXPATH|LOCALPATH|PROJECTNAME|PROVIDER)|VS_WINRT_EXTENSIONS|VS_WINRT_REFERENCES|WIN32_EXECUTABLE|XCODE_ATTRIBUTE_\w+)\b</string>
			<key>name</key>
			<string>entity.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<!-- cf https://github.com/twxs/vs.language.cmake/issues/14 -->
			<string>Escaped Strings</string>
			<key>begin</key>
			<string>\\"</string>
			<key>end</key>
			<string>\\"</string>
			<key>name</key>
			<string>string.source.cmake</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\(.|$)</string>
					<key>name</key>
					<string>constant.character.escape</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>comment</key>
			<string>Normal Strings</string>
			<key>begin</key>
			<string>"</string>
			<key>end</key>
			<string>"</string>
			<key>name</key>
			<string>string.source.cmake</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\(.|$)</string>
					<key>name</key>
					<string>constant.character.escape</string>
				</dict>
			</array>
		</dict>
		
		
		<dict>
			<key>comment</key>
			<string>Derecated keyword</string>
			<key>match</key>
			<string>\bBUILD_NAME\b</string>
			<key>name</key>
			<string>invalid.deprecated.source.cmake</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>Compiler Flags</string>
			<key>match</key>
			<string>\b(?i:(CMAKE_)?(CXX_FLAGS|CMAKE_CXX_FLAGS_DEBUG|CMAKE_CXX_FLAGS_MINSIZEREL|CMAKE_CXX_FLAGS_RELEASE|CMAKE_CXX_FLAGS_RELWITHDEBINFO))\b</string>
			<key>name</key>
			<string>variable.source.cmake</string>
		</dict>
	</array>
	<key>repository</key>
		
		
	<dict>
	
	</dict>
	<key>scopeName</key>
	<string>source.cmake</string>
	<key>uuid</key>
	<string>7aed2d59-22d9-41c8-ba9e-4f178191e380</string>
</dict>
</plist>
