{"fileTypes": [], "injectionSelector": "L:text.html.markdown", "patterns": [{"include": "#mdxCodeBlock"}], "repository": {"mdxCodeBlock": {"begin": "(^|\\G)(\\s*)(\\`{3,}|~{3,})\\s*(?i:(mdx)(\\s+[^`~]*)?$)", "name": "markup.fenced_code.block.markdown", "end": "(^|\\G)(\\2|\\s{0,3})(\\3)\\s*$", "beginCaptures": {"3": {"name": "punctuation.definition.markdown"}, "4": {"name": "fenced_code.block.language.markdown"}, "5": {"name": "fenced_code.block.language.attributes.markdown"}}, "endCaptures": {"3": {"name": "punctuation.definition.markdown"}}, "patterns": [{"begin": "(^|\\G)(\\s*)(.*)", "while": "(^|\\G)(?!\\s*([`~]{3,})\\s*$)", "contentName": "meta.embedded.block.mdx", "patterns": [{"include": "source.mdx"}]}]}}, "scopeName": "source.markdown.mdx.codeblock"}