/*
 * Copyright 2000-2014 JetBrains s.r.o.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package standardDsls

contributor(ctype: 'org.codehaus.groovy.runtime.DefaultGroovyMethods') {
  closureInMethod(method: [name: 'inject', params: ['java.util.Collection<T>', 'groovy.lang.Closure']], params: [acc: null, val: 'T'])

  closureInMethod(method: [name: 'min', params: ['java.util.Collection<T>', 'groovy.lang.Closure']], params: [a: 'T', b: "T"])
  closureInMethod(method: [name: 'min', params: ['java.util.Iterator<T>', 'groovy.lang.Closure']], params: [a: 'T', b: 'T'])
  closureInMethod(method: [name: 'min', params: ['java.util.Map<K, V>', 'groovy.lang.Closure']], params: [a: 'java.util.Map.Entry<K, V>', b: 'java.util.Map.Entry<K, V>'])

  closureInMethod(method: [name: 'min', params: ['java.util.Collection<T>', 'groovy.lang.Closure']], params: [a: 'T'])
  closureInMethod(method: [name: 'min', params: ['java.util.Iterator<T>', 'groovy.lang.Closure']], params: [a: 'T'])
  closureInMethod(method: [name: 'min', params: ['java.util.Map<K, V>', 'groovy.lang.Closure']], params: [a: 'java.util.Map.Entry<K, V>'])

  closureInMethod(method: [name: 'max', params: ['java.util.Collection<T>', 'groovy.lang.Closure']], params: [a: 'T', b: 'T'])
  closureInMethod(method: [name: 'max', params: ['java.util.Iterator<T>', 'groovy.lang.Closure']], params: [a: 'T', b: 'T'])
  closureInMethod(method: [name: 'max', params: ['java.util.Map<K, V>', 'groovy.lang.Closure']], params: [a: 'java.util.Map.Entry<K, V>', b: 'java.util.Map.Entry<K, V>'])

  closureInMethod(method: [name: 'max', params: ['java.util.Collection<T>', 'groovy.lang.Closure']], params: [a: 'T'])
  closureInMethod(method: [name: 'max', params: ['java.util.Iterator<T>', 'groovy.lang.Closure']], params: [a: 'T'])
  closureInMethod(method: [name: 'max', params: ['java.util.Map<K, V>', 'groovy.lang.Closure']], params: [a: 'java.util.Map.Entry<K, V>'])
}
