AdminExecuteSequence = [
('InstallInitialize', None, 1500),
('InstallFinalize', None, 6600),
('InstallFiles', None, 4000),
('InstallAdminPackage', None, 3900),
('FileCost', None, 900),
('CostInitialize', None, 800),
('CostFinalize', None, 1000),
('InstallValidate', None, 1400),
]

AdminUISequence = [
('FileCost', None, 900),
('CostInitialize', None, 800),
('CostFinalize', None, 1000),
('ExecuteAction', None, 1300),
('ExitDialog', None, -1),
('FatalError', None, -3),
('UserExit', None, -2),
]

AdvtExecuteSequence = [
('InstallInitialize', None, 1500),
('InstallFinalize', None, 6600),
('CostInitialize', None, 800),
('CostFinalize', None, 1000),
('InstallValidate', None, 1400),
('CreateShortcuts', None, 4500),
('MsiPublishAssemblies', None, 6250),
('PublishComponents', None, 6200),
('PublishFeatures', None, 6300),
('PublishProduct', None, 6400),
('RegisterClassInfo', None, 4600),
('RegisterExtensionInfo', None, 4700),
('RegisterMIMEInfo', None, 4900),
('RegisterProgIdInfo', None, 4800),
]

InstallExecuteSequence = [
('InstallInitialize', None, 1500),
('InstallFinalize', None, 6600),
('InstallFiles', None, 4000),
('FileCost', None, 900),
('CostInitialize', None, 800),
('CostFinalize', None, 1000),
('InstallValidate', None, 1400),
('CreateShortcuts', None, 4500),
('MsiPublishAssemblies', None, 6250),
('PublishComponents', None, 6200),
('PublishFeatures', None, 6300),
('PublishProduct', None, 6400),
('RegisterClassInfo', None, 4600),
('RegisterExtensionInfo', None, 4700),
('RegisterMIMEInfo', None, 4900),
('RegisterProgIdInfo', None, 4800),
('AllocateRegistrySpace', 'NOT Installed', 1550),
('AppSearch', None, 400),
('BindImage', None, 4300),
('CCPSearch', 'NOT Installed', 500),
('CreateFolders', None, 3700),
('DeleteServices', 'VersionNT', 2000),
('DuplicateFiles', None, 4210),
('FindRelatedProducts', None, 200),
('InstallODBC', None, 5400),
('InstallServices', 'VersionNT', 5800),
('IsolateComponents', None, 950),
('LaunchConditions', None, 100),
('MigrateFeatureStates', None, 1200),
('MoveFiles', None, 3800),
('PatchFiles', None, 4090),
('ProcessComponents', None, 1600),
('RegisterComPlus', None, 5700),
('RegisterFonts', None, 5300),
('RegisterProduct', None, 6100),
('RegisterTypeLibraries', None, 5500),
('RegisterUser', None, 6000),
('RemoveDuplicateFiles', None, 3400),
('RemoveEnvironmentStrings', None, 3300),
('RemoveExistingProducts', None, 6700),
('RemoveFiles', None, 3500),
('RemoveFolders', None, 3600),
('RemoveIniValues', None, 3100),
('RemoveODBC', None, 2400),
('RemoveRegistryValues', None, 2600),
('RemoveShortcuts', None, 3200),
('RMCCPSearch', 'NOT Installed', 600),
('SelfRegModules', None, 5600),
('SelfUnregModules', None, 2200),
('SetODBCFolders', None, 1100),
('StartServices', 'VersionNT', 5900),
('StopServices', 'VersionNT', 1900),
('MsiUnpublishAssemblies', None, 1750),
('UnpublishComponents', None, 1700),
('UnpublishFeatures', None, 1800),
('UnregisterClassInfo', None, 2700),
('UnregisterComPlus', None, 2100),
('UnregisterExtensionInfo', None, 2800),
('UnregisterFonts', None, 2500),
('UnregisterMIMEInfo', None, 3000),
('UnregisterProgIdInfo', None, 2900),
('UnregisterTypeLibraries', None, 2300),
('ValidateProductID', None, 700),
('WriteEnvironmentStrings', None, 5200),
('WriteIniValues', None, 5100),
('WriteRegistryValues', None, 5000),
]

InstallUISequence = [
('FileCost', None, 900),
('CostInitialize', None, 800),
('CostFinalize', None, 1000),
('ExecuteAction', None, 1300),
('ExitDialog', None, -1),
('FatalError', None, -3),
('UserExit', None, -2),
('AppSearch', None, 400),
('CCPSearch', 'NOT Installed', 500),
('FindRelatedProducts', None, 200),
('IsolateComponents', None, 950),
('LaunchConditions', None, 100),
('MigrateFeatureStates', None, 1200),
('RMCCPSearch', 'NOT Installed', 600),
('ValidateProductID', None, 700),
]

tables=['AdminExecuteSequence', 'AdminUISequence', 'AdvtExecuteSequence', 'InstallExecuteSequence', 'InstallUISequence']
