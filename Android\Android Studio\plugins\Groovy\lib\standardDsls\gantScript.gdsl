/*
 * Copyright 2000-2014 JetBrains s.r.o.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package standardDsls

def ctx = context(scope: scriptScope(), scriptType : "gant")

contributor ([ctx], {
  property name:"ant", type:"org.codehaus.gant.GantBuilder"
  property name:"Ant", type:"org.codehaus.gant.GantBuilder"
  property name:"includeTargets", type:"org.codehaus.gant.IncludeTargets"
  property name:"includeTool", type:"org.codehaus.gant.IncludeTool"
  property name:"targetDescriptions", type:"TreeMap"
  property name:"message", type:{}
  property name:"cacheEnabled", type:"Boolean"

  method name:"setDefaultTarget", type:"void", params:[target: {}]
  method name:"depends", type:"void", params:[list: "List"]
  method name:"target", type:"void", params:[args:[:], target: {}]
})
