{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"]], "autoClosingPairs": [{"open": "{", "close": "}", "notIn": ["string"]}, {"open": "[", "close": "]", "notIn": ["string"]}, {"open": "(", "close": ")", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string"]}, {"open": "\"", "close": "\"", "notIn": ["string", "comment"]}, {"open": "`", "close": "`", "notIn": ["string", "comment"]}], "indentationRules": {"increaseIndentPattern": "({+(?=((\\\\.|[^\"\\\\])*\"(\\\\.|[^\"\\\\])*\")*[^\"}]*)$)|(\\[+(?=((\\\\.|[^\"\\\\])*\"(\\\\.|[^\"\\\\])*\")*[^\"\\]]*)$)", "decreaseIndentPattern": "^\\s*[}\\]],?\\s*$"}}