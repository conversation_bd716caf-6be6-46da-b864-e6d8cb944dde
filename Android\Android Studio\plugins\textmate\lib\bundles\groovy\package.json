{"name": "groovy", "version": "1.0.0", "description": "%description%", "license": "MIT", "contributes": {"languages": [{"id": "groovy", "aliases": ["Groovy", "groovy"], "extensions": [".groovy", ".gvy", ".gradle", ".jen<PERSON>le", ".nf"], "filenames": ["Jenkins<PERSON><PERSON>"], "filenamePatterns": ["Jenkinsfile*"], "firstLine": "^#!.*\\bgroovy\\b", "configuration": "./language-configuration.json"}], "grammars": [{"language": "groovy", "scopeName": "source.groovy", "path": "./syntaxes/groovy.tmLanguage.json"}], "snippets": [{"language": "groovy", "path": "./snippets/groovy.code-snippets"}]}}