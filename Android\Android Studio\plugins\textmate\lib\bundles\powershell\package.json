{"name": "powershell", "version": "1.0.0", "description": "%description%", "license": "MIT", "contributes": {"languages": [{"id": "powershell", "extensions": [".ps1", ".psm1", ".psd1", ".pssc", ".psrc"], "aliases": ["PowerShell", "powershell", "ps", "ps1", "pwsh"], "firstLine": "^#!\\s*/.*\\bpwsh\\b", "configuration": "./language-configuration.json"}], "grammars": [{"language": "powershell", "scopeName": "source.powershell", "path": "./syntaxes/powershell.tmLanguage.json"}]}}