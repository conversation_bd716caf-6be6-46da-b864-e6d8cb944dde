{"name": "clojure", "version": "1.0.0", "description": "%description%", "license": "MIT", "contributes": {"languages": [{"id": "clojure", "aliases": ["Clojure", "clojure"], "extensions": [".clj", ".cljs", ".cljc", ".cljx", ".clojure", ".edn"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "clojure", "scopeName": "source.clojure", "path": "./syntaxes/clojure.tmLanguage.json"}], "configurationDefaults": {"[clojure]": {"diffEditor.ignoreTrimWhitespace": false}}}}