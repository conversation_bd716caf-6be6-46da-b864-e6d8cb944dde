{"name": "vscode-bicep", "version": "1.0.0", "description": "Bicep language support", "license": "MIT", "contributes": {"languages": [{"id": "bicep", "aliases": ["Bicep", "bicep"], "extensions": [".bicep"], "configuration": "./syntaxes/language-configuration.json"}, {"id": "jsonc", "filenames": ["bicepconfig.json"]}], "grammars": [{"scopeName": "markdown.bicep.codeblock", "path": "./syntaxes/bicep.codeblock.json", "injectTo": ["text.html.markdown"], "embeddedLanguages": {"meta.embedded.block.bicep": "bicep"}}, {"language": "bicep", "scopeName": "source.bicep", "path": "./syntaxes/bicep.tmlanguage"}]}}