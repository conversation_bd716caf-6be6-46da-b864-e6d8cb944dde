{"name": "cmake", "version": "0.0.17", "description": "CMake language support for Visual Studio Code", "license": "MIT", "contributes": {"languages": [{"id": "cmake", "extensions": [".cmake"], "filenames": ["CMakeLists.txt"], "aliases": ["CMake"]}, {"id": "cmake-cache", "filenames": ["CMakeCache.txt"], "aliases": ["<PERSON><PERSON><PERSON>"]}], "commands": [{"command": "cmake.onlineHelp", "title": "CMake: Online Help"}], "grammars": [{"language": "cmake", "scopeName": "source.cmake", "path": "./syntaxes/CMake.tmLanguage"}, {"language": "cmake-cache", "scopeName": "source.cmakecache", "path": "./syntaxes/CMakeCache.tmLanguage"}], "configuration": {"title": "CMake configuration", "properties": {"cmake.cmakePath": {"type": "string", "default": "cmake", "description": "The path to CMake generator executable"}}}}}