{"apply": {"prefix": "apply", "body": "{% apply %}\n\t$0\n{% endapply %}", "description": "apply", "scope": "text.html.twig"}, "asset": {"prefix": "asset", "body": "{% set asset = ${1:entry.assetFieldHandle}.one() %}\n\n{% if asset %}\n\t<img src=\"{{ asset.getUrl(\"${2:thumb}\") }}\" width=\"{{ asset.getWidth(\"${2:thumb}\") }}\" height=\"{{ asset.getHeight(\"${2:thumb}\") }}\" alt=\"{{ asset.title }}\">\n{% endif %}", "description": "asset", "scope": "text.html.twig"}, "assets": {"prefix": "assets", "body": "{% for image in craft.assets.\n\t.sourceId(\"${1:1}\")\n\t.kind(\"${2:image}\")\n\t.limit(${3:10})\n}).all() %}\n\t<img src=\"{{ image.url${4:(\"${5:thumb}\")} }}\" width=\"${6:200}\" height=\"${7:200}\" alt=\"{{ image.title }}\">\n{% endfor %}\n$0", "description": "craft.assets", "scope": "text.html.twig"}, "autoescape": {"prefix": "autoescape", "body": "{% autoescape \"${1:type}\" %}\n\t$0\n{% endautoescape %}", "description": "autoescape", "scope": "text.html.twig"}, "blockb": {"prefix": "blockb", "body": "{% block ${1:name} %}\n\t$0\n{% endblock %}", "description": "block (block)", "scope": "text.html.twig"}, "block": {"prefix": "block", "body": "{% block ${1:name} %}$0{% endblock %}", "description": "block", "scope": "text.html.twig"}, "blockf": {"prefix": "blockf", "body": "{{ block(\"${1:name}\") }}$0", "description": "blockf", "scope": "text.html.twig"}, "cache": {"prefix": "cache", "body": "{% cache %}\n\t$1\n{% endcache %}\n$0", "description": "cache", "scope": "text.html.twig"}, "case": {"prefix": "case", "body": "{% case \"${1:value}\" %}\n\t$0", "description": "case", "scope": "text.html.twig"}, "children": {"prefix": "children", "body": "{% children %}$0", "description": "children", "scope": "text.html.twig"}, "ceil": {"prefix": "ceil", "body": "ceil($1)$0", "description": "ceil", "scope": "text.html.twig"}, "formlogin": {"prefix": "formlogin", "body": "<form method=\"post\" accept-charset=\"UTF-8\">\n\t{{ csrfInput() }}\n\t<input type=\"hidden\" name=\"action\" value=\"users/login\">\n\n\t<h3><label for=\"loginName\">Username or email</label></h3>\n\t<input id=\"loginName\" type=\"text\" name=\"loginName\"\n\t\tvalue=\"{{ craft.app.user.rememberedUsername }}\">\n\n\t<h3><label for=\"password\">Password</label></h3>\n\t<input id=\"password\" type=\"password\" name=\"password\">\n\n\t<label>\n\t\t<input type=\"checkbox\" name=\"rememberMe\" value=\"1\">\n\t\tRemember me\n\t</label>\n\n\t<input type=\"submit\" value=\"Login\">\n\n\t{% if errorMessage is defined %}\n\t\t<p>{{ errorMessage }}</p>\n\t{% endif %}\n</form>\n\n<p><a href=\"{{ url(\"forgotpassword\") }}\">Forgot your password?</a></p>", "description": "craft.user - example login form", "scope": "text.html.twig"}, "formuserprofile": {"prefix": "formuserprofile", "body": "<form method=\"post\" accept-charset=\"UTF-8\">\n\t{{ csrfInput() }}\n\t<input type=\"hidden\" name=\"action\" value=\"users/save-user\">\n\t{{ redirectInput(\"users/\"~currentUser.username) }}\n\t<input type=\"hidden\" name=\"userId\" value=\"{{ currentUser.id }}\">\n\n\t<label for=\"location\">Location</label>\n\t<input type=\"text\" id=\"location\" name=\"fields[location]\" value=\"{{ currentUser.location }}\">\n\n\t<label for=\"bio\">Bio</label>\n\t<textarea id=\"bio\" name=\"fields[bio]\">{{ currentUser.bio }}</textarea>\n\n\t<input type=\"submit\" value=\"Save Profile\">\n</form>", "description": "craft.user - example user profile form", "scope": "text.html.twig"}, "formuserregistration": {"prefix": "formuserregistration", "body": "<form method=\"post\" accept-charset=\"UTF-8\">\n\t{{ csrfInput() }}\n\t<input type=\"hidden\" name=\"action\" value=\"users/save-user\">\n\t{{ redirectInput(\"\") }}\n\n\t{% macro errorList(errors) %}\n\t\t{% if errors %}\n\t\t\t<ul class=\"errors\">\n\t\t\t\t{% for error in errors %}\n\t\t\t\t\t<li>{{ error }}</li>\n\t\t\t\t{% endfor %}\n\t\t\t</ul>\n\t\t{% endif %}\n\t{% endmacro %}\n\n\t{% from _self import errorList %}\n\n\t<h3><label for=\"username\">Username</label></h3>\n\t<input id=\"username\" type=\"text\" name=\"username\"\n\t\t{%- if user is defined %} value=\"{{ user.username }}\"{% endif -%}>\n\n\t{% if user is defined %}\n\t\t{{ errorList(user.getErrors(\"username\")) }}\n\t{% endif %}\n\n\t<h3><label for=\"email\">Email</label></h3>\n\t<input id=\"email\" type=\"text\" name=\"email\"\n\t\t{%- if user is defined %} value=\"{{ user.email }}\"{% endif %}>\n\n\t{% if user is defined %}\n\t\t{{ errorList(user.getErrors(\"email\")) }}\n\t{% endif %}\n\n\t<h3><label for=\"password\">Password</label></h3>\n\t<input id=\"password\" type=\"password\" name=\"password\">\n\n\t{% if user is defined %}\n\t\t{{ errorList(user.getErrors(\"password\")) }}\n\t{% endif %}\n\n\t<input type=\"submit\" value=\"Register\">\n</form>", "description": "craft.user - example user registration form", "scope": "text.html.twig"}, "formforgotpassword": {"prefix": "formforgotpassword", "body": "<form method=\"post\" accept-charset=\"UTF-8\">\n\t{{ csrfInput() }}\n\t<input type=\"hidden\" name=\"action\" value=\"users/send-password-reset-email\">\n\t{{ redirectInput(\"\") }}\n\n\t<h3><label for=\"loginName\">Username or email</label></h3>\n\t<input id=\"loginName\" type=\"text\" name=\"loginName\"\n\t\tvalue=\"{% if loginName is defined %}{{ loginName }}{% else %}{{ craft.app.user.rememberedUsername }}{% endif %}\">\n\n\t{% if errors is defined %}\n\t\t<ul class=\"errors\">\n\t\t\t{% for error in errors %}\n\t\t\t\t<li>{{ error }}</li>\n\t\t\t{% endfor %}\n\t\t</ul>\n\t{% endif %}\n\n\t<input type=\"submit\" value=\"Submit\">\n</form>", "description": "craft.user - example forgot password form", "scope": "text.html.twig"}, "formsetpassword": {"prefix": "formsetpassword", "body": "<form method=\"post\" accept-charset=\"UTF-8\">\n\t{{ csrfInput() }}\n\t<input type=\"hidden\" name=\"action\" value=\"users/set-password\">\n\t<input type=\"hidden\" name=\"code\" value=\"{{ code }}\">\n\t<input type=\"hidden\" name=\"id\" value=\"{{ id }}\">\n\n\t<h3><label for=\"newPassword\">New Password</label></h3>\n\t<input id=\"newPassword\" type=\"password\" name=\"newPassword\">\n\t{% if errors is defined %}\n\t\t<ul class=\"errors\">\n\t\t\t{% for error in errors %}\n\t\t\t\t<li>{{ error }}</li>\n\t\t\t{% endfor %}\n\t\t</ul>\n\t{% endif %}\n\n\t<input type=\"submit\" value=\"Submit\">\n</form>", "description": "craft.user - example set password form", "scope": "text.html.twig"}, "formsearch": {"prefix": "formsearch", "body": "<form action=\"{{ url(\"search/results\") }}\">\n\t<input type=\"search\" name=\"q\" placeholder=\"Search\">\n\t<input type=\"submit\" value=\"Go\">\n</form>", "description": "craft.entries - example search form", "scope": "text.html.twig"}, "formsearchresults": {"prefix": "formsearchresults", "body": "<h1>Search Results</h1>\n\n{% set query = craft.app.request.getParam(\"q\") %}\n{% set entries = craft.entries.search(query).orderBy(\"score\").all() %}\n\n{% if entries | length %}\n\t<p>{{ entries | length }} results:</p>\n\n\t<ul>\n\t\t{% for entry in entries %}\n\t\t\t<li><a href=\"{{ entry.url }}\">{{ entry.title }}</a></li>\n\t\t{% endfor %}\n\t</ul>\n{% else %}\n\t<p>Your search for “{{ query }}” didn’t return any results.</p>\n{% endif %}", "description": "craft.entries - example search results", "scope": "text.html.twig"}, "rss": {"prefix": "rss", "body": "<?xml version=\"1.0\"?>\n<rss version=\"2.0\" xmlns:atom=\"http://www.w3.org/2005/Atom\">\n\t<channel>\n\t\t<title>{{ siteName }}</title>\n\t\t<link>{{ siteUrl }}</link>\n\t\t<atom:link href=\"{{ craft.app.request.absoluteUrl }}\" rel=\"self\" type=\"application/rss+xml\" />\n\t\t<description>{{ globals.siteDescription }}</description>\n\t\t<language>en-us</language>\n\t\t<pubDate>{{ now | rss }}</pubDate>\n\t\t<lastBuildDate>{{ now | rss }}</lastBuildDate>\n\n\t\t{% for entry in craft.entries.all() %}\n\t\t\t<item>\n\t\t\t\t<title>{{ entry.title }}</title>\n\t\t\t\t<link>{{ entry.url }}</link>\n\t\t\t\t<pubDate>{{ entry.postDate | rss }}</pubDate>\n\t\t\t\t<author>{{ entry.author }}</author>\n\t\t\t\t<guid>{{ entry.url }}</guid>\n\t\t\t\t<description><![CDATA[\n\t\t\t\t\t{{ entry.body }}\n\t\t\t\t]]></description>\n\t\t\t</item>\n\t\t{% endfor %}\n\t</channel>\n</rss>", "description": "craft.entries - example rss feed", "scope": "text.html.twig"}, "assetso": {"prefix": "assetso", "body": "{% set assets = craft.assets({\n\tsourceId: \"${1:1}\",\n\tkind: \"${2:image}\",\n\tlimit: ${3:10}\n}).all() %}\n\n{% for image in assets %}\n\t<img src=\"{{ image.url${4:(\"${5:thumb}\")} }}\" width=\"${6:200}\" height=\"${7:200}\" alt=\"{{ image.title }}\">\n{% endfor %}\n$0", "description": "craft.assets - object syntax", "scope": "text.html.twig"}, "categorieso": {"prefix": "categorieso", "body": "{% set categories = craft.categories({\n\tgroup: \"${1:categoryGroupHandle}\",\n\tlimit: \"${2:11}\"\n}).all() %}\n\n<ul>\n\t{% nav category in categories %}\n\t\t<li>\n\t\t\t<a href=\"{{ category.url }}\">{{ category.title }}</a>\n\t\t\t{% ifchildren %}\n\t\t\t\t<ul>\n\t\t\t\t\t{% children %}\n\t\t\t\t</ul>\n\t\t\t{% endifchildren %}\n\t\t</li>\n\t{% endnav %}\n</ul>", "description": "craft.categories - object syntax", "scope": "text.html.twig"}, "categories": {"prefix": "categories", "body": "<ul>\n\t{% nav category in craft.categories\n\t\t.group(\"${1:categoryGroupHandle}\")\n\t\t.limit(${2:11})\n\t\t.all()\n\t%}\n\t\t<li>\n\t\t\t<a href=\"{{ category.url }}\">{{ category.title }}</a>\n\t\t\t{% ifchildren %}\n\t\t\t\t<ul>\n\t\t\t\t\t{% children %}\n\t\t\t\t</ul>\n\t\t\t{% endifchildren %}\n\t\t</li>\n\t{% endnav %}\n</ul>", "description": "craft.categories", "scope": "text.html.twig"}, "entrieso": {"prefix": "entrieso", "body": "{% set entries = craft.entries({\n\tsection: \"${1:sectionName}\",\n\tlimit: \"${2:10}\"\n}).all() %}\n\n{% for entry in entries %}\n\t<a href=\"{{ entry.url }}\">{{ entry.title }}</a>\n{% endfor %}\n$0", "description": "craft.entries - object syntax", "scope": "text.html.twig"}, "entries": {"prefix": "entries", "body": "{% for entry in craft.entries\n\t.section(\"${1:sectionName}\")\n\t.limit(${2:10})\n\t.all()\n%}\n\t<a href=\"{{ entry.url }}\">{{ entry.title }}</a>\n{% endfor %}\n$0", "description": "craft.entries", "scope": "text.html.twig"}, "feed": {"prefix": "feed", "body": "{% set feedUrl = \"${1:http://feeds.feedburner.com/blogandtonic}\" %}\n{% set limit = ${2:10} %}\n{% set items = craft.feeds.getFeedItems(feedUrl, limit).all() %}\n\n{% for item in items %}\n\t<article>\n\t\t<h3><a href=\"{{ item.permalink }}\">{{ item.title }}</a></h3>\n\t\t<p class=\"author\">{{ item.authors[0].name }}</p>\n\t\t<p class=\"date\">{{ item.date }}</p>\n\n\t\t{{ item.summary }}\n\t</article>\n{% endfor %}", "description": "feed", "scope": "text.html.twig"}, "t": {"prefix": "t", "body": "{{ $1 | t }}$0", "description": "translate with | t"}, "replace": {"prefix": "replace", "body": "{{ ${1:$TM_SELECTED_TEXT} | replace(\"search\", \"replace\") }}$0", "description": "replace with | replace(\"search\", \"replace\")"}, "replacex": {"prefix": "replacex", "body": "{{ ${1:$TM_SELECTED_TEXT} | replace(\"/(search)/i\", \"replace\") }}$0", "description": "replace regex with | replace(\"/(search)/i\", \"replace\")"}, "split": {"prefix": "split", "body": "{{ ${1:$TM_SELECTED_TEXT} | split(\"\\n\") }}$0", "description": "split on | split (\"\\n\")"}, "tagso": {"prefix": "tagso", "body": "{% set tags = craft.tags({\n\tgroup: \"${1:tagGroupHandle}\"\n}).all() %}\n\n<ul>\n\t{% for tag in tags %}\n\t\t<li>{{ tag }}</a></li>\n\t{% endfor %}\n</ul>\n$0", "description": "craft.tags - object syntax", "scope": "text.html.twig"}, "tags": {"prefix": "tags", "body": "<ul>\n\t{% for tag in craft.tags.group(\"${1:tagGroupHandle}\").all() %}\n\t\t<li>{{ tag }}</li>\n\t{% endfor %}\n</ul>\n$0", "description": "craft.tags", "scope": "text.html.twig"}, "userso": {"prefix": "userso", "body": "{% set users = craft.users({\n\tgroup: \"${1:userGroupHandle}\"\n}).all() %}\n\n{% for user in users %}\n\t{{ user.firstName }} {{ user.lastName }}\n{% endfor %}\n$0", "description": "craft.users - object syntax", "scope": "text.html.twig"}, "users": {"prefix": "users", "body": "{% for user in craft.users.group(\"${1:userGroupHandle}\").all() %}\n\t{{ user.firstName }} {{ user.lastName }}\n{% endfor %}\n$0", "description": "craft.users", "scope": "text.html.twig"}, "csrf": {"prefix": "csrf", "body": "{{ csrfInput() }}\n$0", "description": "csrf", "scope": "text.html.twig"}, "dd": {"prefix": "dd", "body": "<pre>\n\t{{ dump($1) }}\n</pre>\n{% exit %}$0", "description": "dump and die", "scope": "text.html.twig"}, "do": {"prefix": "do", "body": "{% do $1 %}$0", "description": "do", "scope": "text.html.twig"}, "dojs": {"prefix": "dojs", "body": "{% do view.registerJsFile \"${1:url}\" %}$0", "description": "do js", "scope": "text.html.twig"}, "docss": {"prefix": "docss", "body": "{% do view.registerCssFile \"${1:url}\" %}$0", "description": "do css", "scope": "text.html.twig"}, "dump": {"prefix": "dump", "body": "<pre>\n\t{{ dump($1) }}\n</pre>", "description": "dump", "scope": "text.html.twig"}, "else": {"prefix": "else", "body": "{% else %}\n\t$0", "description": "else", "scope": "text.html.twig"}, "elseif": {"prefix": "elseif", "body": "{% elseif %}\n\t$0", "description": "elseif", "scope": "text.html.twig"}, "embed": {"prefix": "embed", "body": "{% embed \"${1:template}\" %}\n\t$0\n{% endembed %}", "description": "embed", "scope": "text.html.twig"}, "endapply": {"prefix": "endapply", "body": "{% endapply %}$0", "description": "endapply", "scope": "text.html.twig"}, "endautoescape": {"prefix": "endautoescape", "body": "{% endautoescape %}$0", "description": "endautoescape", "scope": "text.html.twig"}, "endblock": {"prefix": "endblock", "body": "{% endblock %}$0", "description": "endblock", "scope": "text.html.twig"}, "endcache": {"prefix": "endcache", "body": "{% endcache %}$0", "description": "endcache", "scope": "text.html.twig"}, "endembed": {"prefix": "endembed", "body": "{% endembed %}$0", "description": "endembed", "scope": "text.html.twig"}, "endfilter": {"prefix": "endfilter", "body": "{% endfilter %}$0", "description": "endfilter", "scope": "text.html.twig"}, "endfor": {"prefix": "endfor", "body": "{% endfor %}$0", "description": "endfor", "scope": "text.html.twig"}, "endif": {"prefix": "endif", "body": "{% endif %}$0", "description": "endif", "scope": "text.html.twig"}, "endifchildren": {"prefix": "endifchildren", "body": "{% endifchildren %}$0", "description": "endifchildren", "scope": "text.html.twig"}, "endcss": {"prefix": "endcss", "body": "{% endcss %}$0", "description": "endcss", "scope": "text.html.twig"}, "endjs": {"prefix": "endjs", "body": "{% endjs %}$0", "description": "endjs", "scope": "text.html.twig"}, "endmacro": {"prefix": "endmacro", "body": "{% endmacro %}$0", "description": "endmacro", "scope": "text.html.twig"}, "endnav": {"prefix": "endnav", "body": "{% endnav %}$0", "description": "endnav", "scope": "text.html.twig"}, "endset": {"prefix": "endset", "body": "{% endset %}$0", "description": "endset", "scope": "text.html.twig"}, "endspaceless": {"prefix": "endspaceless", "body": "{% endspaceless %}$0", "description": "endspaceless", "scope": "text.html.twig"}, "endswitch": {"prefix": "endswitch", "body": "{% endswitch %}$0", "description": "endswitch", "scope": "text.html.twig"}, "endtrans": {"prefix": "endtrans", "body": "{% endtrans %}$0", "description": "endtrans", "scope": "text.html.twig"}, "endverbatim": {"prefix": "endverbatim", "body": "{% endverbatim %}$0", "description": "endverbatim", "scope": "text.html.twig"}, "exit": {"prefix": "exit", "body": "{% exit ${1:404} %}", "description": "exit", "scope": "text.html.twig"}, "extends": {"prefix": "extends", "body": "{% extends \"${1:template}\" %}$0", "description": "extends", "scope": "text.html.twig"}, "filterb": {"prefix": "filterb", "body": "{% filter ${1:name} %}\n\t$0\n{% endfilter %}", "description": "filter (block)", "scope": "text.html.twig"}, "filter": {"prefix": "filter", "body": "{% filter ${1:name} %}$0{% endfilter %}", "description": "filter", "scope": "text.html.twig"}, "floor": {"prefix": "floor", "body": "floor($1)$0", "description": "floor", "scope": "text.html.twig"}, "fore": {"prefix": "fore", "body": "{% for ${1:item} in ${2:items} %}\n\t$3\n{% else %}\n\t$0\n{% endfor %}", "description": "for ... else", "scope": "text.html.twig"}, "for": {"prefix": "for", "body": "{% for ${1:item} in ${2:items} %}\n\t$0\n{% endfor %}", "description": "for", "scope": "text.html.twig"}, "from": {"prefix": "from", "body": "{% from \"${1:template}\" import \"${2:macro}\" %}$0", "description": "from", "scope": "text.html.twig"}, "endbody": {"prefix": "endbody", "body": "{{ endBody() }}\n$0", "description": "endBody", "scope": "text.html.twig"}, "head": {"prefix": "head", "body": "{{ head() }}\n$0", "description": "head", "scope": "text.html.twig"}, "if": {"prefix": "if", "body": "{% if ${1:condition} %}$2{% endif %}\n$0", "description": "if", "scope": "text.html.twig"}, "ifb": {"prefix": "ifb", "body": "{% if ${1:condition} %}\n\t$0\n{% endif %}", "description": "if (block)", "scope": "text.html.twig"}, "ife": {"prefix": "ife", "body": "{% if ${1:condition} %}\n\t$2\n{% else %}\n\t$0\n{% endif %}", "description": "if ... else", "scope": "text.html.twig"}, "if1": {"prefix": "if", "body": "{% if ${1:condition} %}$0{% endif %}", "description": "if", "scope": "text.html.twig"}, "ifchildren": {"prefix": "ifchildren", "body": "{% ifchildren %}\n\t$1\n{% endifchildren %}\n$0", "description": "ifchildren", "scope": "text.html.twig"}, "import": {"prefix": "import", "body": "{% import \"${1:template}\" as ${2:name} %}$0", "description": "import", "scope": "text.html.twig"}, "importself": {"prefix": "importself", "body": "{% import _self as ${1:name} %}$0", "description": "importself", "scope": "text.html.twig"}, "inckv": {"prefix": "inckv", "body": "{% include \"${1:template}\" with {\n\t${2:key}: ${3:\"${4:value}\"}\n} %}\n$0", "description": "include w/ key/value", "scope": "text.html.twig"}, "include": {"prefix": "include", "body": "{% include \"${1:template}\" %}$0", "description": "include", "scope": "text.html.twig"}, "inc": {"prefix": "inc", "body": "{% include \"${1:template}\" %}$0", "description": "inc", "scope": "text.html.twig"}, "incp": {"prefix": "incp", "body": "{% include \"${1:template}\"${2: with ${3:params} }%}$0", "description": "include w/ params", "scope": "text.html.twig"}, "css1": {"prefix": "css", "body": "{% do view.registerCssFile(\"${1:/resources/css/global.css}\") %}\n$0", "description": "registerCssFile", "scope": "text.html.twig"}, "js": {"prefix": "js", "body": "{% js %}\n\t$1\n{% endjs %}\n$0", "description": "js", "scope": "text.html.twig"}, "js1": {"prefix": "js", "body": "{% do view.registerJsFile(\"${1:/resources/js/global.js}\") %}\n$0", "description": "registerJsFile", "scope": "text.html.twig"}, "css": {"prefix": "css", "body": "{% css %}\n\t$1\n{% endcss %}\n$0", "description": "css", "scope": "text.html.twig"}, "macro": {"prefix": "macro", "body": "{% macro ${1:name}(${2:params}) %}\n\t$0\n{% endmacro %}", "description": "macro", "scope": "text.html.twig"}, "matrix": {"prefix": "matrix", "body": "{% for block in ${1:entry.matrixFieldHandle}.all() %}\n\n\t{% if block.type == \"${2:blockHandle}\" %}\n\t\t{{ block.${3:fieldHandle} }}\n\t{% endif %}\n\n\t{% if block.type == \"${4:blockHandle}\" %}\n\t\t{{ block.${5:fieldHandle} }}\n\t{% endif %}\n\n{% endfor %}\n$0", "description": "matrix", "scope": "text.html.twig"}, "matrixif": {"prefix": "matrixif", "body": "{% for block in ${1:entry.matrixFieldHandle}.all() %}\n\n\t{% if block.type == \"${2:blockHandle}\" %}\n\t\t{{ block.${3:fieldHandle} }}\n\t{% endif %}\n\n\t{% if block.type == \"${4:blockHandle}\" %}\n\t\t{{ block.${5:fieldHandle} }}\n\t{% endif %}\n\n{% endfor %}\n$0", "description": "matrixif", "scope": "text.html.twig"}, "matrixifelse": {"prefix": "matrixifelse", "body": "{% for block in ${1:entry.matrixFieldHandle}.all() %}\n\n\t{% if block.type == \"${2:blockHandle}\" %}\n\n\t\t{{ block.${3:fieldHandle} }}\n\n\t{% elseif block.type == \"${4:blockHandle}\" %}\n\n\t\t$0\n\t\n\t{% endif %}\n\n{% endfor %}", "description": "matrixifelse", "scope": "text.html.twig"}, "matrixswitch": {"prefix": "matrixswitch", "body": "{% for block in ${1:entry.matrixFieldHandle}.all() %}\n\n\t{% switch block.type %}\n\n\t\t{% case \"${2:blockHandle}\" %}\n\n\t\t\t{{ block.${3:fieldHandle} }}\n\n\t\t{% case \"${4:blockHandle}\" %}\n\n\t\t\t$0\n\n\t{% endswitch %}\n\n{% endfor %}", "description": "matrixswitch", "scope": "text.html.twig"}, "max": {"prefix": "max", "body": "max(${1:$2, $3})$0", "description": "max", "scope": "text.html.twig"}, "min": {"prefix": "min", "body": "min(${1:$2, $3})$0", "description": "min", "scope": "text.html.twig"}, "nav": {"prefix": "nav", "body": "{% nav ${1:item} in ${2:items} %}\n\t$3\n{% endnav %}\n$0", "description": "nav", "scope": "text.html.twig"}, "paginate": {"prefix": "paginate", "body": "{% paginate ${1:elements} as ${2:pageInfo}, ${3:pageEntries} %}\n\n{% for item in ${3:pageEntries} %}\n\t$0\n{% endfor %}\n\n{% if ${2:pageInfo}.prevUrl %}<a href=\"{{ ${2:pageInfo}.prevUrl }}\">Previous Page</a>{% endif %}\n{% if ${2:pageInfo}.nextUrl %}<a href=\"{{ ${2:pageInfo}.nextUrl }}\">Next Page</a>{% endif %}", "description": "paginate simple", "scope": "text.html.twig"}, "paginate1": {"prefix": "paginate", "body": "{# PAGINATION\n\t\t\nFor this pagination to work properly, we need to be sure to set\nthe paginateBase variable in the template we are including the \npagination in.\n\n{% set paginateBase = \"/blog/p\" %}\n#}\n\n{% if pageInfo.totalPages > 1 %}\n<ul>\n\t{% if pageInfo.currentPage != \"1\" %}\n\t\t<li><a href=\"{{ paginateBase ~ \"1\" }}\">First Page</a></li>\n\t{% endif %}\n\n\t{% if pageInfo.prevUrl %}\n\t\t<li><a href=\"{{ pageInfo.prevUrl }}\">Previous Page</a></li>\n\t{% endif %}\n\n\t{% for pageNumber in 1..pageInfo.totalPages %}\n\t\t<li {% if pageInfo.currentPage == pageNumber %}class=\"active-page\"{% endif %}>\n\t\t\t<a href=\"{{ paginateBase ~ pageNumber }}\">{{ pageNumber }}</a>\n\t\t</li>\n\t{% endfor %}\n\n\t{% if pageInfo.nextUrl %}\n\t\t<li><a href=\"{{ pageInfo.nextUrl }}\">Next Page</a></li>\n\t{% endif %}\n\n\t{% if pageInfo.currentPage != pageInfo.total %}\n\t\t<li><a href=\"{{ paginateBase ~ pageInfo.total }}\">Last Page</a></li>\n\t{% endif %}\n</ul>\n{% endif %}\n$0", "description": "paginate advanced", "scope": "text.html.twig"}, "redirect": {"prefix": "redirect", "body": "{% redirect \"${1:template/path or http://straightupcraft.com}\" %}\n$0", "description": "redirect", "scope": "text.html.twig"}, "getparam": {"prefix": "<PERSON><PERSON><PERSON>", "body": "craft.app.request.getParam(${1:\"Query String or Post Variable Name\"})\n$0", "description": "request getParam", "scope": "text.html.twig"}, "getbodyparam": {"prefix": "get<PERSON><PERSON><PERSON>", "body": "craft.app.request.getBodyParam(${1:\"postVariableName\"})\n$0", "description": "request getBodyParam", "scope": "text.html.twig"}, "getqueryparam": {"prefix": "getqueryparam", "body": "craft.app.request.getQueryParam(${1:\"queryStringName\"})\n$0", "description": "request getQueryParam", "scope": "text.html.twig"}, "getsegment": {"prefix": "getsegment", "body": "craft.app.request.getSegment(${1:2})\n$0", "description": "request getSegment", "scope": "text.html.twig"}, "requirelogin": {"prefix": "requirelogin", "body": "{% requireLogin %}\n$0", "description": "requireLogin", "scope": "text.html.twig"}, "requirepermission": {"prefix": "requirepermission", "body": "{% requirePermission \"${1:spendTheNight}\" %}\n$0", "description": "requirePermission", "scope": "text.html.twig"}, "round": {"prefix": "round", "body": "{{ $1 | round(1, 'floor') }}$0", "description": "round", "scope": "text.html.twig"}, "setb": {"prefix": "setb", "body": "{% set ${1:var} %}\n\t$0\n{% endset %}", "description": "set (block)", "scope": "text.html.twig"}, "set": {"prefix": "set", "body": "{% set ${1:var} = ${2:value} %}$0", "description": "set", "scope": "text.html.twig"}, "shuffle": {"prefix": "shuffle", "body": "shuffle($1)$0", "description": "shuffle", "scope": "text.html.twig"}, "random": {"prefix": "random", "body": "random($1)$0", "description": "random", "scope": "text.html.twig"}, "spaceless": {"prefix": "spaceless", "body": "{% spaceless %}\n\t$0\n{% endspaceless %}", "description": "spaceless", "scope": "text.html.twig"}, "switch": {"prefix": "switch", "body": "{% switch ${1:variable} %}\n\n\t{% case \"${2:value1}\" %}\n\t\n\n\t{% case \"${3:value2}\" %}\n\t\n\n\t{% default %}\n\t\n\n{% endswitch %}\n$0", "description": "switch", "scope": "text.html.twig"}, "trans": {"prefix": "trans", "body": "{% trans %}$0{% endtrans %}", "description": "trans", "scope": "text.html.twig"}, "urla": {"prefix": "urla", "body": "url(\"${1:path}\", ${2:{foo:\"1\", bar:\"2\"\\}}, ${3:\"http\"}, ${4:false})$0", "description": "url w/ arguments", "scope": "text.html.twig"}, "url": {"prefix": "url", "body": "url(\"${1:path}\")$0", "description": "url", "scope": "text.html.twig"}, "use": {"prefix": "use", "body": "{% use \"${1:template}\" %}$0", "description": "use", "scope": "text.html.twig"}, "verbatim": {"prefix": "verbatim", "body": "{% verbatim %}\n\t$0\n{% endverbatim %}", "description": "verbatim", "scope": "text.html.twig"}}