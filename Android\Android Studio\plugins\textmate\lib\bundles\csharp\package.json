{"name": "csharp", "version": "1.0.0", "description": "%description%", "license": "MIT", "contributes": {"configurationDefaults": {"[csharp]": {"editor.maxTokenizationLineLength": 2500}}, "languages": [{"id": "csharp", "extensions": [".cs", ".csx", ".cake"], "aliases": ["C#", "csharp"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "csharp", "scopeName": "source.cs", "path": "./syntaxes/csharp.tmLanguage.json"}], "snippets": [{"language": "csharp", "path": "./snippets/csharp.code-snippets"}]}}